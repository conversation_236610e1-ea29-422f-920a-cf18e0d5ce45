#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版远程摄像头查看器
直接通过网络协议访问远程摄像头（RTSP/HTTP等）
"""

import cv2
import numpy as np
import threading
import time
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import queue
import requests
from requests.auth import HTTPBasicAuth

class SimpleCameraViewer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("远程摄像头查看器 - 简化版")
        self.root.geometry("1200x900")
        
        # 连接参数
        self.remote_ip = tk.StringVar(value="*************")
        self.username = tk.StringVar(value="admin")
        self.password = tk.StringVar(value="password")
        self.port = tk.StringVar(value="8080")
        
        # 摄像头相关
        self.camera_streams = {}
        self.camera_threads = {}
        self.frame_queues = {}
        self.camera_labels = {}
        self.running = False
        
        # 摄像头URL模板
        self.camera_urls = {
            0: "rtsp://{username}:{password}@{ip}:{port}/cam1",
            1: "rtsp://{username}:{password}@{ip}:{port}/cam2", 
            2: "rtsp://{username}:{password}@{ip}:{port}/cam3",
            3: "rtsp://{username}:{password}@{ip}:{port}/cam4"
        }
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建GUI界面"""
        # 连接配置框架
        config_frame = ttk.LabelFrame(self.root, text="连接配置", padding="10")
        config_frame.pack(fill="x", padx=10, pady=5)
        
        # 第一行：基本连接信息
        row1_frame = ttk.Frame(config_frame)
        row1_frame.pack(fill="x", pady=2)
        
        ttk.Label(row1_frame, text="远程IP:").pack(side="left", padx=5)
        ttk.Entry(row1_frame, textvariable=self.remote_ip, width=15).pack(side="left", padx=5)
        
        ttk.Label(row1_frame, text="端口:").pack(side="left", padx=5)
        ttk.Entry(row1_frame, textvariable=self.port, width=8).pack(side="left", padx=5)
        
        ttk.Label(row1_frame, text="用户名:").pack(side="left", padx=5)
        ttk.Entry(row1_frame, textvariable=self.username, width=12).pack(side="left", padx=5)
        
        ttk.Label(row1_frame, text="密码:").pack(side="left", padx=5)
        ttk.Entry(row1_frame, textvariable=self.password, width=12, show="*").pack(side="left", padx=5)
        
        # 第二行：URL配置
        row2_frame = ttk.Frame(config_frame)
        row2_frame.pack(fill="x", pady=2)
        
        ttk.Label(row2_frame, text="URL模板:").pack(side="left", padx=5)
        self.url_template = tk.StringVar(value="rtsp://{username}:{password}@{ip}:{port}/cam{id}")
        ttk.Entry(row2_frame, textvariable=self.url_template, width=50).pack(side="left", padx=5)
        
        # 第三行：控制按钮
        row3_frame = ttk.Frame(config_frame)
        row3_frame.pack(fill="x", pady=5)
        
        self.test_btn = ttk.Button(row3_frame, text="测试连接", command=self.test_connection)
        self.test_btn.pack(side="left", padx=5)
        
        self.start_btn = ttk.Button(row3_frame, text="启动摄像头", command=self.start_cameras)
        self.start_btn.pack(side="left", padx=5)
        
        self.stop_btn = ttk.Button(row3_frame, text="停止摄像头", command=self.stop_cameras, state="disabled")
        self.stop_btn.pack(side="left", padx=5)
        
        # 状态标签
        self.status_label = ttk.Label(row3_frame, text="就绪", foreground="blue")
        self.status_label.pack(side="left", padx=20)
        
        # 摄像头显示框架
        camera_frame = ttk.LabelFrame(self.root, text="摄像头画面", padding="10")
        camera_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 创建2x2网格显示四个摄像头
        for i in range(4):
            row = i // 2
            col = i % 2
            
            # 摄像头标签框架
            cam_frame = ttk.LabelFrame(camera_frame, text=f"摄像头 {i+1}")
            cam_frame.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")
            
            # 摄像头画面标签
            cam_label = ttk.Label(cam_frame, text="摄像头未启动", anchor="center", background="black", foreground="white")
            cam_label.pack(fill="both", expand=True)
            
            self.camera_labels[i] = cam_label
            self.frame_queues[i] = queue.Queue(maxsize=2)
        
        # 配置网格权重
        camera_frame.grid_rowconfigure(0, weight=1)
        camera_frame.grid_rowconfigure(1, weight=1)
        camera_frame.grid_columnconfigure(0, weight=1)
        camera_frame.grid_columnconfigure(1, weight=1)
        
        # 日志框架
        log_frame = ttk.LabelFrame(self.root, text="日志信息", padding="5")
        log_frame.pack(fill="x", padx=10, pady=5)
        
        self.log_text = tk.Text(log_frame, height=6, wrap="word")
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def get_camera_url(self, camera_id):
        """获取摄像头URL"""
        template = self.url_template.get()
        return template.format(
            username=self.username.get(),
            password=self.password.get(),
            ip=self.remote_ip.get(),
            port=self.port.get(),
            id=camera_id + 1
        )
        
    def test_connection(self):
        """测试连接"""
        self.log_message("开始测试连接...")
        
        # 测试第一个摄像头
        test_url = self.get_camera_url(0)
        self.log_message(f"测试URL: {test_url}")
        
        try:
            # 尝试连接摄像头
            cap = cv2.VideoCapture(test_url)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    self.log_message("✓ 连接测试成功！")
                    self.status_label.config(text="连接正常", foreground="green")
                else:
                    self.log_message("✗ 无法读取视频帧")
                    self.status_label.config(text="连接异常", foreground="orange")
            else:
                self.log_message("✗ 无法打开视频流")
                self.status_label.config(text="连接失败", foreground="red")
            cap.release()
            
        except Exception as e:
            self.log_message(f"✗ 连接测试失败: {str(e)}")
            self.status_label.config(text="连接失败", foreground="red")
            
    def start_cameras(self):
        """启动所有摄像头"""
        self.running = True
        self.log_message("启动摄像头...")
        
        # 启动四个摄像头线程
        for i in range(4):
            thread = threading.Thread(target=self.camera_thread, args=(i,), daemon=True)
            thread.start()
            self.camera_threads[i] = thread
            
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        # 启动显示更新线程
        self.display_thread = threading.Thread(target=self.update_display, daemon=True)
        self.display_thread.start()
        
        self.status_label.config(text="运行中", foreground="green")
        self.log_message("所有摄像头已启动")
        
    def stop_cameras(self):
        """停止所有摄像头"""
        self.running = False
        self.log_message("停止摄像头...")
        
        # 停止所有摄像头流
        for i in range(4):
            if i in self.camera_streams:
                if self.camera_streams[i] is not None:
                    self.camera_streams[i].release()
                del self.camera_streams[i]
                
        self.camera_threads.clear()
        
        # 清空显示
        for i in range(4):
            self.camera_labels[i].config(image="", text="摄像头已停止")
            
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.status_label.config(text="已停止", foreground="red")
        
        self.log_message("所有摄像头已停止")
        
    def camera_thread(self, camera_id):
        """摄像头线程函数"""
        url = self.get_camera_url(camera_id)
        self.log_message(f"摄像头 {camera_id + 1} 连接到: {url}")
        
        try:
            cap = cv2.VideoCapture(url)
            
            if not cap.isOpened():
                self.log_message(f"摄像头 {camera_id + 1} 连接失败")
                return
                
            self.camera_streams[camera_id] = cap
            self.log_message(f"摄像头 {camera_id + 1} 连接成功")
            
            while self.running and camera_id in self.camera_streams:
                ret, frame = cap.read()
                if ret:
                    # 调整帧大小
                    frame = cv2.resize(frame, (320, 240))
                    
                    # 将帧放入队列
                    if not self.frame_queues[camera_id].full():
                        try:
                            self.frame_queues[camera_id].put_nowait(frame)
                        except queue.Full:
                            pass
                else:
                    self.log_message(f"摄像头 {camera_id + 1} 读取帧失败")
                    time.sleep(1)
                    
        except Exception as e:
            self.log_message(f"摄像头 {camera_id + 1} 出错: {str(e)}")
        finally:
            if camera_id in self.camera_streams:
                self.camera_streams[camera_id].release()
                
    def update_display(self):
        """更新显示线程"""
        while self.running:
            try:
                for i in range(4):
                    if not self.frame_queues[i].empty():
                        frame = self.frame_queues[i].get_nowait()
                        
                        # 转换为PIL图像
                        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                        pil_image = Image.fromarray(frame_rgb)
                        photo = ImageTk.PhotoImage(pil_image)
                        
                        # 更新标签
                        self.camera_labels[i].config(image=photo, text="")
                        self.camera_labels[i].image = photo  # 保持引用
                        
                time.sleep(0.033)  # 约30fps
                
            except Exception as e:
                self.log_message(f"更新显示出错: {str(e)}")
                time.sleep(0.1)
                
    def run(self):
        """运行应用程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("程序启动完成")
        self.root.mainloop()
        
    def on_closing(self):
        """关闭应用程序时的清理工作"""
        self.stop_cameras()
        self.root.destroy()

if __name__ == "__main__":
    app = SimpleCameraViewer()
    app.run()
